import * as FileSystem from "expo-file-system";
import ButtonComponent from "@/component/ButtonComponent";
import LinearGradientComponent from "@/component/LinearGradientComponent";
import OtpTextInput from "react-native-text-input-otp";
import PrivacyPolicyComponent from "@/component/PrivacyPolicyComponent";
import React, { useEffect, useRef, useState } from "react";
import useGetApiDatawithParam from "../../../../../../../hooks/useGetApiDatawithParam";
import useTenStackHook from "@/hooks/TenStackHook/TenStackHook";
import useTenStackMutate from "@/hooks/useTenStackMutate/useTenStackMutate";
import useUpdateWithFormData2 from "../../../../../../../hooks/useUpdateWithFormData2";
import { AVPlaybackStatus, ResizeMode, Video } from "expo-av";
import { router, useLocalSearchParams } from "expo-router";
import { ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useOrderState } from "../../../../../../../store/Order";
import { useLogin } from "@/store";

import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  TouchableWithoutFeedback,
  Alert,
} from "react-native";

const Otp = () => {
  const { invoice_no, id, orderNumber, video } = useLocalSearchParams<{
    invoice_no: string;
    id: string;
    orderNumber: string;
    video: string;
  }>();
  const [otp, setOtp] = useState("");
  const [Isotpfilled, setotpfilled] = useState(false);
  const [status, setStatus] = useState({});
  const videoRef = useRef<Video>(null);
  const [videoUri, setVideoUri] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    prepareVideo();
  }, [video]);

  const prepareVideo = async () => {
    try {
      setIsLoading(true);

      if (!video) {
        throw new Error("No video URI provided");
      }

      // Ensure the video path is properly formatted
      let properVideoUri = video;
      if (!video.startsWith("file://")) {
        properVideoUri = `file://${video}`;
      }

      // Check if the file exists
      const fileInfo = await FileSystem.getInfoAsync(properVideoUri);
      console.log("Video file info:", fileInfo);

      if (!fileInfo.exists) {
        throw new Error("Video file not found");
      }

      // Copy video to a new location to ensure it's accessible
      const newPath = `${
        FileSystem.cacheDirectory
      }temp_video_${Date.now()}.mp4`;
      await FileSystem.copyAsync({
        from: properVideoUri,
        to: newPath,
      });

      setVideoUri(newPath);

      // Load the video
      if (videoRef.current) {
        await videoRef.current.unloadAsync();
        await videoRef.current.loadAsync(
          { uri: newPath },
          { shouldPlay: true },
          false
        );
      }
    } catch (error) {
      console.error("Error preparing video:", error);
      Alert.alert("Error", "Failed to load video file");
    } finally {
      setIsLoading(false);
    }
  };

  const check = () => {
    if (otp.length == 4) {
      setotpfilled(true);
    } else {
      setotpfilled(false);
    }
  };

  const { data, isLoading: invoice_detailsIsLoding } = useTenStackHook<
    { invoice_no: string | string[] },
    any
  >({
    endpoint: "order/invoice_details",
    params: {
      invoice_no: invoice_no,
    },
    canSave: true,
    key: "invoice_details",
    id: invoice_no as string,
  });

  const { mutate: UpdateBankDetails, isLoading: isUpdating } =
    useTenStackMutate({
      endpoint: "order/pickup_submit",
      invalidateQueriesKey: ["invoice_details"],
    });

  const formdatas = new FormData();

  useEffect(() => {
    check();
    if (videoRef.current) {
      loadVideo();
    }
  }, [otp, video]);

  useEffect(() => {
    console.log("Video URI:", video);

    // Check if video file exists
    const checkVideoFile = async () => {
      try {
        const fileInfo = await FileSystem.getInfoAsync(video);
        console.log("Video file info:", fileInfo);
      } catch (error) {
        console.error("Error checking video file:", error);
      }
    };

    checkVideoFile();
  }, [video]);

  const loadVideo = async () => {
    try {
      if (videoRef.current) {
        await videoRef.current.unloadAsync();
        await videoRef.current.loadAsync(
          { uri: video },
          { shouldPlay: true },
          false
        );
      }
    } catch (error) {
      console.error("Error loading video:", error);
      Alert.alert("Error", "Failed to load video");
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <SafeAreaView
        style={{
          flex: 1,
          position: "relative",
          justifyContent: "space-between",
        }}
      >
        {/* LinearGradient Component Area*/}
        <LinearGradientComponent />
        <View className="z-10 px-6 flex-1">
          {/* Login Title Area */}
          <View className="items-center justify-end h-[24%] mb-8 gap-3">
            <View>
              <Text className="font-[400] text-[26px] text-white">
                Enter OTP
              </Text>
            </View>
            <View className="flex-row items-center justify-center">
              <Text className="font-[400] text-[18px] text-white">
                Enter OTP given by the customer
              </Text>
            </View>
          </View>
          {/* Otp Field Area */}
          <View className="items-center justify-center">
            <View
              style={{
                maxWidth: "90%",
              }}
            >
              <OtpTextInput
                otp={otp}
                setOtp={setOtp}
                digits={4}
                style={{
                  borderRadius: 0,
                  borderTopWidth: 0,
                  borderRightWidth: 0,
                  borderLeftWidth: 0,
                  height: 45,
                }}
                fontStyle={{ fontSize: 26, fontWeight: "bold", color: "#fff" }}
                focusedStyle={{ borderColor: "#5cb85c", borderBottomWidth: 2 }}
              />
            </View>
          </View>

          <View
            className=""
            style={{
              flex: 0.4,
              justifyContent: "flex-end",
            }}
          >
            <ButtonComponent
              text="Verify OTP"
              value={Isotpfilled}
              disabled={isUpdating}
              style={{ opacity: isUpdating ? 0.5 : 1 }}
              pressfun={async () => {
                try {
                  if (!Isotpfilled) return;
                  if (!video) {
                    Alert.alert("Error", "Video file is missing");
                    return;
                  }

                  const formdatas = new FormData();
                  formdatas.append("invoice_no", invoice_no.toString());
                  formdatas.append("shop_id", id.toString());
                  formdatas.append("otp", otp);
                  formdatas.append(
                    "customer_id",
                    (data as any)?.deliveryAddress?.user_id.toString() ?? ""
                  );
                  formdatas.append("latitude", (22.568007).toString());
                  formdatas.append("longitude", (88.371993).toString());

                  // Create a proper file object with all required properties
                  formdatas.append("file", {
                    uri: videoUri,
                    type: "video/mp4",
                    name: `video_${Date.now()}.mp4`,
                    originalname: `video_${Date.now()}.mp4`,
                  } as any);
                  console.log(formdatas);

                  // Call the mutation function
                  UpdateBankDetails(formdatas, {
                    onSuccess: (result: any) => {
                      if (!result) {
                        Alert.alert("Error", "Server response was empty");
                        return;
                      }
                      console.log(result);
                      if (result.status === 200) {
                        router.replace({
                          pathname: "/home/<USER>",
                          params: {
                            invoice_no: invoice_no,
                            orderNumber: orderNumber,
                          },
                        });
                      } else {
                        Alert.alert("Error", result.msg || "Upload failed");
                      }
                    },
                    onError: (error: any) => {
                      Alert.alert(
                        "Upload Failed",
                        `Failed to upload: ${error?.message || "Unknown error"}`
                      );
                    },
                  });
                } catch (error) {
                  // This catch block will only handle errors that occur before the UpdateBankDetails call
                  const errorMessage =
                    error instanceof Error
                      ? error.message
                      : "An unexpected error occurred";

                  Alert.alert(
                    "Error",
                    `Failed to prepare upload: ${errorMessage}`,
                    [{ text: "OK" }]
                  );
                  console.error("Submit Preparation Error:", error);
                }
              }}
            />
          </View>
        </View>
      </SafeAreaView>
    </TouchableWithoutFeedback>
  );
};

export default Otp;
